<template>
  <div class="mobileOverview">

    <div class="banner">
      <img src="../../assets/images/banneroverviewip.png" >
    </div>

    <div class="mobile-pd over">
      <div class="profile poas animate__animated animate__fadeInDown">{{profile.title}}</div>
      <div class="center-text animate__animated animate__fadeInDown">{{profile.conter}}</div>
      <div style="display: flex;">
        <button class="profile-but animate__animated animate__fadeInDown" @click="toCompanyPro">{{profile.buttext}}</button>
      </div>
    </div>

    <div class="mobile-bg mobile-pd">
      <div class="mobile-bg-title poas animate__animated" ref="quoteTop">{{quote.titleLest}}</div>
      <div class="quote-bon-f animate__animated" ref="quoteBonFt">
        <div class="quote-nasdaq">{{quote.nasdaq}}</div>
        <div class="quote-code">
          <span>${{resData&&resData.QLastPrice}}</span>
          <span :style="{color: colorFunc()}">{{ changeFunc() }}</span>
              <span :style="{color: colorFunc()}">{{ rateFunc() }}</span>
        </div>
      </div>
      <div class="mobile-bg-title poas mt animate__animated" ref="quoteTopR">{{quote.titleRight}}</div>

      <div class="quote-bon-f animate__animated" ref="quoteBonFb" style="display: flex;">
        <img class="pdfimg" src="../../assets/images/pdf.png">
        <span class="quote-bon-r">{{quote.grounp}}</span>
      </div>
    </div>

    <div class="news mobile-pd">
      <div class="profile poas animate__animated" ref="newsH">{{news.h}}</div>
      <div class="news-list animate__animated" ref="newList" v-for="(item,index) in news.data.slice(0,3)" :key="index+1" @click="toNewsDetail(item.id)">
        <div class="news-date">{{item.date}}</div>
        <div class="news-title">{{item.title}}</div>
        <!-- <div class="news-con">{{item.con}}</div> -->
        <div class="companyNewsConBox">
          <div v-html="item.con"></div>
        </div>
      </div>
    </div>

  </div>
</template>

<script>
import { profile,quote,news } from "@/pagesData/Overview/pages";
  export default {
    props:['resData'],
    data() {
      return {
        profile,
        quote,
        news,
      }
    },
    methods:{
      handleScroll(){
        let scrollTop = window.pageXOffset || document.documentElement.scrollTop || document.body.scrollTop
        if(scrollTop>=150&&scrollTop<500){
            this.$refs.quoteTopR.classList.add('animate__fadeInDown')
            this.$refs.quoteTop.classList.add('animate__fadeInDown')
            this.$refs.quoteBonFt.classList.add('animate__fadeInDown')
            this.$refs.quoteBonFb.classList.add('animate__fadeInDown')
        }else if(scrollTop>=500){
            this.$refs.newsH.classList.add('animate__fadeInDown')
            if(this.$refs.newList.length!=0){
              this.$refs.newList[0].classList.add('animate__fadeInDown')
              this.$refs.newList[1].classList.add('animate__fadeInDown')
            }
        }
      },
      toCompanyPro(){
        this.$router.push('/CompanyProfile')
      },
      toNewsDetail(id){
        this.$router.push({
          path: '/CompanyNewsDetail',
          query: {
            id: id,
          }
        })
      },
      colorFunc(){
        if(this.resData.QLastPrice > this.resData.QPreClosingPrice){
          return 'green'
        }else if(this.resData.QLastPrice < this.resData.QPreClosingPrice){
          return '#ff583d'
        }
        return '#ccc'
      },
      rateFunc(){
        if(this.resData.QLastPrice > this.resData.QPreClosingPrice){
          return this.resData&&this.resData.QChangeRate&&'+('+ this.resData.QChangeRate+')%'
        }else if(this.resData.QLastPrice < this.resData.QPreClosingPrice){
          return this.resData&&this.resData.QChangeRate&&'('+ this.resData.QChangeRate+')%'
        }
        return 0
      },
      changeFunc(){
        if(this.resData.QLastPrice > this.resData.QPreClosingPrice){
          return this.resData&&this.resData.QChangeValue&&'+'+this.resData.QChangeValue
        }else if(this.resData.QLastPrice < this.resData.QPreClosingPrice){
          return this.resData&&this.resData.QChangeValue
        }
        return 0
      }
    },
    mounted(){
      window.addEventListener('scroll', this.handleScroll)
    },
    beforeDestroy(){
      window.removeEventListener('scroll',this.handleScroll)
    }
  }
</script>

<style lang="less" scoped>
  .mobileOverview{
    .over{
      overflow: hidden;
    }
    .profile{
      position: relative;
      font-size:0.18rem;
      font-family:MicrosoftYaHei-Bold,MicrosoftYaHei;
      font-weight:bold;
      color:rgba(51,51,51,1);
      line-height:0.24rem;
      letter-spacing:0.01rem;
      margin-top: 0.37rem;
      margin-bottom: 0.2rem;
      text-transform: uppercase;
    }
    .center-text{
      font-size:0.15rem;
      font-family:MicrosoftYaHei;
      color:rgba(51,51,51,1);
      line-height:0.2rem;
    }
     .profile-but{
      width:1.5rem;
      height:0.4rem;
      border:0.01rem solid rgba(227,146,27,1);
      background: #fff;
      margin: 0.3rem auto 0.5rem;
      font-size:0.14rem;
      font-family:MicrosoftYaHei;
      color:rgba(227,146,27,1);
      line-height:0.27rem;
      outline: none;
      cursor: pointer;
      &:hover{
        border-color: #D37F04;
        color: #D37F04;
      }
    }
    .mobile-bg{
      padding-bottom: 0.53rem;
      padding-top: 0.37rem;
      width: 3.75rem;
      background: url('../../assets/images/banner_bgquoteip.png');
      background-size:100% 100%;  
    }
    .mobile-bg-title{
      position: relative;
      font-size:0.18rem;
      font-family:MicrosoftYaHei-Bold,MicrosoftYaHei;
      font-weight:bold;
      color:rgba(255,255,255,1);
      line-height:0.24rem;
      letter-spacing:0.01rem;
    }
    .mt{
      margin-top: 0.67rem;
    }
    .quote-bon-f{
      padding-left: 0.2rem;
      margin-top: 0.36rem;
    }
    .quote-nasdaq{
      font-size:0.13rem;
      font-family:NotoSansHans-Medium,NotoSansHans;
      font-weight:500;
      color:rgba(255,255,255,1);
      line-height:0.2rem;
    }
    .quote-code{
      margin-top: 0.16rem;
      span:nth-child(1){
        font-size:0.4rem;
        font-family:NotoSansHans-Bold,NotoSansHans;
        font-weight:bold;
        color:rgba(255,255,255,1);
        line-height:0.6rem;
        letter-spacing:0.02rem;
        margin-right: 0.1rem;
      }
      span:nth-child(n+2){
        font-size:0.15rem;
        font-family:NotoSansHans-Medium,NotoSansHans;
        font-weight:500;
        color:rgba(255,88,61,1);
        line-height:0.23rem;
      }
      span:nth-child(3){
        margin-left: 0.1rem;
      }
    }
    .quote-bon-f{
      padding-left: 0.19rem;
      img{
        height: 0.16rem;
        width: 0.16rem;
      }
      .quote-bon-r{
        font-size:0.16rem;
        font-family:NotoSansHans-Bold,NotoSansHans;
        font-weight:bold;
        color:rgba(255,255,255,1);
        line-height:0.2rem;
        margin-left: 0.1rem;
      }
    }
    .news{
      overflow: hidden;
      padding-bottom: 0.4rem;
      .profile{
        margin-bottom: 0 !important;
      }
    }
    .news-date{
      margin-top: 0.4rem;
      font-size:0.13rem;
      font-family:MicrosoftYaHei-Bold,MicrosoftYaHei;
      font-weight:bold;
      color:rgba(119,119,119,1);
      line-height:0.17rem;
      margin-bottom: 0.18rem;
    }
    .news-title{
      font-size:0.18rem;
      font-family:MicrosoftYaHei-Bold,MicrosoftYaHei;
      font-weight:bold;
      color:rgba(227,146,27,1);
      line-height:0.24rem;
      letter-spacing:0.01rem;
      margin-bottom: 0.15rem;
    }
  }
  .mobile-pd{
    padding: 0 0.1rem;
  }
  .poas::before{
    position: absolute;
    left: 0;
    top: -0.04rem;
    content: "";
    width:0.26rem;
    height:0.03rem;
    background:rgba(236,177,67,1);
  }
</style>