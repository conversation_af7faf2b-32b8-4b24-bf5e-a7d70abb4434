<template>
  <div class="companyNews">
    <div class="banner">
      <img
        class="bannerImg"
        :src="fullWidth > 1024 ? bannerNews : bannerNewsM"
      />
    </div>
    <section>
      <div class="companyNewsImg">
        <p>
          {{ content.title }}
        </p>
        <div class="yearFilter">
          <div class="yearTips">year</div>
          <Select
            v-model="yearId"
            style="width:200px"
            @on-change="selectChange"
          >
            <Option v-for="item in yearList" :value="item.id" :key="item.id">{{
              item.year
            }}</Option>
          </Select>
          <Button type="primary" class="filterBtn" @click="filterFunc"
            >Filter</Button
          >
        </div>
      </div>
      <section
        class="companyNewsCon"
        v-for="(item, key) in newsList"
        :key="key"
        @click="toDetail(item.id)"
      >
        <div class="companyNewsConDate">
          {{ item.date }}
        </div>
        <div class="companyNewsConTitle">
          {{ item.title }}
        </div>
        <div class="companyNewsConBox">
          <div v-html="item.con"></div>
        </div>
      </section>
    </section>
  </div>
</template>
<script>
import { news } from "@/pagesData/Overview/pages";
export default {
  name: "companyNews",
  data() {
    return {
      content: news,
      newsList: news.data,
      fullWidth: document.documentElement.clientWidth,
      bannerNews: require("@/assets/images/bannerNews.png"),
      bannerNewsM: require("@/assets/images/bannerNewsM.png"),
      yearId: 2025,
      yearList: [
        {
          id: 2025,
          year: 2025,
        },
        {
          id: 2024,
          year: 2024,
        },
        {
          id: 2023,
          year: 2023,
        },
        {
          id: 2022,
          year: 2022,
        },
        {
          id: 2021,
          year: 2021,
        },
        {
          id: 2020,
          year: 2020,
        },
      ],
    };
  },
  components: {},
  created() {
    this.filterFunc();
  },
  mounted() {
    window.onresize = () => {
      return (() => {
        this.fullWidth = document.documentElement.clientWidth;
      })();
    };
  },
  methods: {
    toDetail(id) {
      this.$router.push({
        path: "/CompanyNewsDetail",
        query: {
          id: id,
        },
      });
    },
    // 选中年份
    selectChange(id) {
      this.yearId = id;
    },
    // 确定选择
    filterFunc() {
      this.newsList = news.data;
      this.newsList = this.newsList.filter((item, key) => {
        return item.year == this.yearId;
      });
    },
  },
};
</script>
<style lang="less" scoped>
.companyNews {
  .banner {
    width: 100%;

    &Img {
      display: block;
      width: 100%;
    }
  }

  &Img {
    width: 100%;
    padding: 0.7rem 0 0.6rem;

    p {
      max-width: 12rem;
      font-size: 0.4rem;
      color: #333;
      font-weight: 700;
      margin: 0 auto;
      position: relative;

      &::before {
        content: "";
        width: 0.6rem;
        height: 0.06rem;
        background-color: #ecb143;
        position: absolute;
        top: 0;
        left: 0;
      }
    }

    .yearFilter {
      max-width: 12rem;
      color: #333;
      font-weight: 700;
      margin: 0.4rem auto 0;

      .yearTips {
        font-weight: 300;
        font-size: 0.2rem;
        padding-bottom: 4px;
      }

      /deep/ .ivu-select-dropdown {
        background-color: #fff;
      }

      .filterBtn {
        background-color: #ecb143;
        border: none;
        margin-left: 0.1rem;
        padding: 0 0.3rem;

        &:hover {
          background-color: #fff;
          color: #ecb143;
          border: 1px solid #ecb143;
        }
      }

      /deep/ .ivu-select-selection-focused,
      /deep/ .ivu-select-selection:hover {
        border-color: #ecb143;
      }
    }
  }

  &Con {
    max-width: 12rem;
    margin: 0 auto;
    cursor: pointer;

    &Date {
      color: #777;
      font-size: 20px;
      padding-bottom: 0.3rem;
    }

    &Title {
      color: #e3921b;
      font-size: 25px;
      font-weight: 600;
      padding-bottom: 0.3rem;
    }

    &Box {
      color: #333;
      font-size: 16px;
      margin-bottom: 0.6rem;
    }
  }
}

@media screen and (max-width: 1023px) {
  .companyNews {
    &Img {
      width: 100%;
      padding: 0.4rem 0 0.3rem;

      p {
        padding: 0 0.1rem;
        font-size: 0.28rem;

        &::before {
          left: 0.1rem;
        }
      }

      .yearFilter {
        color: #333;
        margin: 0.4rem auto 0;
        padding: 0 0.1rem;

        .yearTips {
          font-weight: 300;
          font-size: 0.2rem;
          padding-bottom: 4px;
        }

        /deep/ .ivu-select-dropdown {
          background-color: #fff;
        }

        .filterBtn {
          background-color: #ecb143;
          border: none;
          margin-left: 0.1rem;
          padding: 0 0.3rem;

          &:hover {
            background-color: #fff;
            color: #ecb143;
            border: 1px solid #ecb143;
          }
        }

        /deep/ .ivu-select-selection-focused,
        /deep/ .ivu-select-selection:hover {
          border-color: #ecb143;
        }
      }
    }

    &Con {
      padding: 0 0.1rem;

      &Date {
        color: #777;
        font-size: 18px;
        padding-bottom: 0.24rem;
      }

      &Title {
        color: #e3921b;
        font-size: 22px;
        padding-bottom: 0.24rem;
      }

      &Box {
        color: #333;
        font-size: 14px;
        margin-bottom: 0.5rem;
      }
    }
  }
}
</style>
